import React, { useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import { RefreshCw, Target, Database } from 'lucide-react'
import * as echarts from 'echarts'
import { TaskSetsMetrics } from '../../../services/management/managementService'

interface TaskSetsEChartsCardProps {
  data: TaskSetsMetrics | null
  loading: boolean
  error: string | null
  onRefresh: () => void
  cardVariants: any
}

// ECharts Sunburst Component with smooth animations
const TaskSetsEChartsSunburst: React.FC<{ data: TaskSetsMetrics }> = ({ data }) => {
  const chartRef = useRef<HTMLDivElement>(null)
  const chartInstance = useRef<echarts.ECharts | null>(null)

  useEffect(() => {
    if (!chartRef.current) return

    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current)
    }

    if (data?.sunburst_data) {
      setupSunburstChart(data.sunburst_data)
    }

    return () => {
      chartInstance.current?.dispose()
      chartInstance.current = null
    }
  }, [data])

  const setupSunburstChart = (sunburstData: any[]) => {
    if (!chartInstance.current || !sunburstData.length) return

    // Enhanced color palette to match Nivo design
    const colorPalette = [
      '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57',
      '#ff9ff3', '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43',
      '#10ac84', '#ee5a24', '#0abde3', '#3867d6', '#8854d0',
      '#f368e0', '#ff3838', '#2ed573', '#1e90ff', '#ffa502'
    ]

    // Transform data for ECharts sunburst format with enhanced colors
    const transformData = (node: any, level: number = 0, colorIndex: number = 0): any => {
      const transformed: any = {
        name: node.name === 'Total Task Sets-2-Curated' ? 'Total Task Sets' : node.name,
        value: node.value,
        itemStyle: {
          color: node.itemStyle?.color || colorPalette[colorIndex % colorPalette.length],
          borderColor: '#ffffff',
          borderWidth: level === 0 ? 4 : 3,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.1)'
        }
      }

      if (node.children && Array.isArray(node.children)) {
        transformed.children = node.children.map((child: any, index: number) =>
          transformData(child, level + 1, colorIndex * 3 + index + 1)
        )
      }

      return transformed
    }

    const chartData = transformData(sunburstData[0])

    const option = {
      backgroundColor: 'transparent',
      animationDuration: 2000,
      animationEasing: 'cubicOut',
      series: {
        type: 'sunburst',
        data: [chartData],
        radius: [0, '90%'],
        center: ['50%', '50%'],
        sort: undefined,
        emphasis: {
          focus: 'ancestor',
          blurScope: 'coordinateSystem',
          itemStyle: {
            shadowBlur: 20,
            shadowColor: 'rgba(0, 0, 0, 0.3)'
          }
        },
        levels: [
          // Root level
          {
            r0: 0,
            r: '12%',
            itemStyle: {
              borderWidth: 4,
              borderColor: '#ffffff',
              shadowBlur: 15,
              shadowColor: 'rgba(0, 0, 0, 0.2)'
            },
            label: {
              rotate: 0,
              fontSize: 14,
              fontWeight: 'bold',
              color: '#ffffff',
              textShadowBlur: 2,
              textShadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          },
          // First level
          {
            r0: '12%',
            r: '40%',
            itemStyle: {
              borderWidth: 3,
              borderColor: '#ffffff',
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.15)'
            },
            label: {
              rotate: 'radial',
              fontSize: 11,
              fontWeight: 'bold',
              color: '#ffffff',
              textShadowBlur: 1,
              textShadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          // Second level
          {
            r0: '40%',
            r: '70%',
            itemStyle: {
              borderWidth: 2,
              borderColor: '#ffffff',
              shadowBlur: 8,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            },
            label: {
              rotate: 'radial',
              fontSize: 10,
              color: '#ffffff',
              textShadowBlur: 1,
              textShadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          },
          // Third level
          {
            r0: '70%',
            r: '90%',
            itemStyle: {
              borderWidth: 2,
              borderColor: '#ffffff',
              shadowBlur: 5,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            },
            label: {
              rotate: 'radial',
              fontSize: 9,
              color: '#ffffff',
              textShadowBlur: 1,
              textShadowColor: 'rgba(0, 0, 0, 0.3)'
            }
          }
        ],
        label: {
          rotate: 'radial',
          fontSize: 11,
          fontWeight: 'bold',
          color: '#ffffff'
        },
        itemStyle: {
          borderColor: '#ffffff',
          borderWidth: 2,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.1)'
        }
      },
      tooltip: {
        trigger: 'item',
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        borderRadius: 8,
        textStyle: { color: '#374151', fontSize: 12 },
        extraCssText: 'box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);',
        formatter: function (params: any) {
          const data = params.data
          return `
            <div style="padding: 12px; min-width: 150px;">
              <div style="font-weight: bold; margin-bottom: 8px; color: #1f2937; font-size: 14px;">${params.name}</div>
              <div style="margin-bottom: 4px;">Task Sets: <strong style="color: #3b82f6;">${params.value}</strong></div>
              ${data.task_items_count ? `<div style="margin-bottom: 4px;">Task Items: <strong style="color: #10b981;">${data.task_items_count}</strong></div>` : ''}
              ${data.task_items_count ? `<div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #e5e7eb; font-size: 11px; color: #6b7280;">Avg Items/Set: <strong>${(data.task_items_count / params.value).toFixed(1)}</strong></div>` : ''}
            </div>
          `
        }
      }
    }

    chartInstance.current.setOption(option, true)
    console.log('ECharts Sunburst: Enhanced chart updated successfully')
  }

  if (!data?.sunburst_data || data.sunburst_data.length === 0) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <Database className="h-12 w-12 text-muted-foreground mx-auto" />
          <p className="text-muted-foreground">No data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full w-full bg-gradient-to-br from-slate-50/50 via-blue-50/30 to-purple-50/50 dark:from-slate-900/20 dark:via-blue-900/15 dark:to-purple-900/20 rounded-xl p-2 border border-slate-200/50 dark:border-slate-700/30">
      <div
        ref={chartRef}
        style={{ height: '100%', width: '100%' }}
      />
    </div>
  )
}

/**
 * Task Sets ECharts Analytics Card - ECharts sunburst visualization
 */
const TaskSetsEChartsCard: React.FC<TaskSetsEChartsCardProps> = ({
  data,
  loading,
  error,
  onRefresh,
  cardVariants
}) => {

  if (loading) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-muted/20 to-transparent animate-pulse" />
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-5 bg-muted rounded w-32 animate-pulse" />
              <div className="h-4 bg-muted rounded w-48 animate-pulse" />
            </div>
            <div className="h-8 w-8 bg-muted rounded animate-pulse" />
          </div>
          <div className="flex-1 bg-muted rounded animate-pulse" />
        </div>
      </motion.div>
    )
  }

  if (error) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Target className="h-5 w-5" />
                Task Sets Distribution (ECharts)
              </h3>
              <p className="text-sm text-muted-foreground">Animated sunburst chart</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <p className="text-destructive">Failed to load chart data</p>
              <button
                onClick={onRefresh}
                className="text-sm text-primary hover:underline"
              >
                Try again
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  if (!data) {
    return (
      <motion.div
        variants={cardVariants}
        className="bg-card border border-border rounded-xl p-6 relative overflow-hidden h-full"
      >
        <div className="relative space-y-6 h-full">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
                <Target className="h-5 w-5" />
                Task Sets Distribution (ECharts)
              </h3>
              <p className="text-sm text-muted-foreground">Animated sunburst chart</p>
            </div>
            <button
              onClick={onRefresh}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className="h-4 w-4 text-muted-foreground" />
            </button>
          </div>
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center space-y-2">
              <Database className="h-12 w-12 text-muted-foreground mx-auto" />
              <p className="text-muted-foreground">No data available</p>
            </div>
          </div>
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      variants={cardVariants}
      className="bg-gradient-to-br from-white/90 via-blue-50/60 to-purple-50/70 dark:from-gray-900/90 dark:via-blue-900/30 dark:to-purple-900/40 border border-blue-200/60 dark:border-blue-800/40 rounded-2xl p-6 relative overflow-hidden h-full shadow-xl backdrop-blur-sm"
    >
      {/* Enhanced Background gradient with animation */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-100/30 via-purple-50/20 to-indigo-100/30 dark:from-blue-900/20 dark:via-purple-900/10 dark:to-indigo-900/20 rounded-2xl animate-pulse" style={{ animationDuration: '4s' }} />

      <div className="relative space-y-6 h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Target className="h-5 w-5 text-blue-600" />
              Task Sets Distribution (ECharts)
            </h3>
            <p className="text-sm text-muted-foreground">Animated sunburst chart</p>
          </div>
          <button
            onClick={onRefresh}
            className="p-2 hover:bg-muted rounded-lg transition-colors group"
            title="Refresh data"
          >
            <RefreshCw className="h-4 w-4 text-muted-foreground group-hover:rotate-180 transition-transform duration-500" />
          </button>
        </div>

        {/* Chart Container - Larger size */}
        <div className="flex-1 min-h-0" style={{ minHeight: '400px' }}>
          <TaskSetsEChartsSunburst data={data} />
        </div>
      </div>
    </motion.div>
  )
}

export default TaskSetsEChartsCard
